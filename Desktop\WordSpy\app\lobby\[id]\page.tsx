'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { io, Socket } from 'socket.io-client';
import { Lobby, Player, PLAYER_COLORS } from '@/types/game';

export default function LobbyPage() {
  const [socket, setSocket] = useState<Socket | null>(null);
  const [lobby, setLobby] = useState<Lobby | null>(null);
  const [currentPlayer, setCurrentPlayer] = useState<Player | null>(null);
  const [error, setError] = useState('');
  const router = useRouter();
  const params = useParams();
  const lobbyId = params?.id ? (params.id as string) : '';

  useEffect(() => {
    const username = localStorage.getItem('wordspy-username');
    if (!username) {
      router.push('/');
      return;
    }

    // Initialize socket connection
    const newSocket = io();
    setSocket(newSocket);

    newSocket.on('connect', () => {
      console.log('Connected to server');
      
      const player: Player = {
        id: '',
        username,
        color: PLAYER_COLORS[Math.floor(Math.random() * PLAYER_COLORS.length)],
        isHost: false,
        isConnected: true
      };

      newSocket.emit('join-lobby', { lobbyId, player });
    });

    newSocket.on('lobby-updated', (updatedLobby: Lobby) => {
      setLobby(updatedLobby);
      const player = updatedLobby.players.find(p => p.username === username);
      setCurrentPlayer(player || null);
    });

    newSocket.on('game-started', (updatedLobby: Lobby) => {
      setLobby(updatedLobby);
      router.push(`/game/${lobbyId}`);
    });

    newSocket.on('error', (message: string) => {
      setError(message);
      setTimeout(() => setError(''), 5000);
    });

    return () => {
      newSocket.close();
    };
  }, [lobbyId, router]);

  const handleStartGame = () => {
    if (socket && currentPlayer?.isHost) {
      socket.emit('start-game', { lobbyId });
    }
  };

  const handleColorChange = (color: string) => {
    if (socket) {
      socket.emit('update-color', { lobbyId, color });
    }
  };

  const handleLeaveLobby = () => {
    router.push('/');
  };

  if (!lobby) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Indlæser lobby...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="container mx-auto px-4 py-8">
        <header className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">{lobby.name}</h1>
          <p className="text-gray-600">Venter på spillere...</p>
          <button
            onClick={handleLeaveLobby}
            className="mt-2 text-sm text-blue-500 hover:text-blue-700"
          >
            ← Tilbage til Lobbies
          </button>
        </header>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6 max-w-2xl mx-auto">
            {error}
          </div>
        )}

        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-bold mb-4">
              Spillere ({lobby.players.filter(p => p.isConnected).length}/{lobby.maxPlayers})
            </h2>
            <div className="space-y-3">
              {lobby.players.filter(p => p.isConnected).map((player) => (
                <div
                  key={player.id}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="w-6 h-6 rounded-full border-2 border-gray-300"
                      style={{ backgroundColor: player.color }}
                    ></div>
                    <span className="font-medium">{player.username}</span>
                    {player.isHost && (
                      <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                        Vært
                      </span>
                    )}
                  </div>
                  {player.username === currentPlayer?.username && (
                    <div className="flex gap-1">
                      {PLAYER_COLORS.map((color) => (
                        <button
                          key={color}
                          onClick={() => handleColorChange(color)}
                          className={`w-6 h-6 rounded-full border-2 ${
                            player.color === color ? 'border-gray-800' : 'border-gray-300'
                          }`}
                          style={{ backgroundColor: color }}
                        />
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {currentPlayer?.isHost && (
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-bold mb-4">Spil Indstillinger</h3>
              <div className="space-y-4">
                <div className="text-sm text-gray-600">
                  • Mindst 3 spillere er nødvendige for at starte
                  • En tilfældig spiller vil være imposter
                  • Spillere tager tur til at beskrive deres ord
                  • Stem for at eliminere imposteren
                </div>
                <button
                  onClick={handleStartGame}
                  disabled={lobby.players.filter(p => p.isConnected).length < 3}
                  className="w-full px-6 py-3 bg-green-500 text-white rounded-lg font-semibold hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
                >
                  {lobby.players.filter(p => p.isConnected).length < 3
                    ? `Skal bruges ${3 - lobby.players.filter(p => p.isConnected).length} flere spillere`
                    : 'Start Spil'
                  }
                </button>
              </div>
            </div>
          )}

          {!currentPlayer?.isHost && (
            <div className="bg-white rounded-lg shadow-md p-6 text-center">
              <p className="text-gray-600">Venter på at vært starter spillet...</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
